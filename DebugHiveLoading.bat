@echo off
echo ========================================
echo Hive Loading Diagnostic Script
echo ========================================
echo.

echo [%time%] Starting hive loading diagnostics...
echo.

echo [%time%] Checking current environment...
echo Current drive: %CD%
echo System drive: %SystemDrive%
echo Windows directory: %SystemRoot%
echo.

echo [%time%] Checking for offline Windows installations...
echo.

echo Checking C: drive...
if exist "C:\Windows\System32\config\SAM" (
    echo ✓ Found Windows installation on C: drive
    echo   SAM file: C:\Windows\System32\config\SAM
    echo   SECURITY file: C:\Windows\System32\config\SECURITY
    echo.
    echo File sizes:
    dir "C:\Windows\System32\config\SAM" | find "SAM"
    dir "C:\Windows\System32\config\SECURITY" | find "SECURITY"
    echo.
) else (
    echo ✗ No Windows installation found on C: drive
    echo.
)

echo Checking D: drive...
if exist "D:\Windows\System32\config\SAM" (
    echo ✓ Found Windows installation on D: drive
    echo   SAM file: D:\Windows\System32\config\SAM
    echo   SECURITY file: D:\Windows\System32\config\SECURITY
    echo.
    echo File sizes:
    dir "D:\Windows\System32\config\SAM" | find "SAM"
    dir "D:\Windows\System32\config\SECURITY" | find "SECURITY"
    echo.
) else (
    echo ✗ No Windows installation found on D: drive
    echo.
)

echo Checking E: drive...
if exist "E:\Windows\System32\config\SAM" (
    echo ✓ Found Windows installation on E: drive
    echo   SAM file: E:\Windows\System32\config\SAM
    echo   SECURITY file: E:\Windows\System32\config\SECURITY
    echo.
    echo File sizes:
    dir "E:\Windows\System32\config\SAM" | find "SAM"
    dir "E:\Windows\System32\config\SECURITY" | find "SECURITY"
    echo.
) else (
    echo ✗ No Windows installation found on E: drive
    echo.
)

echo [%time%] Testing file access...
echo.

if exist "C:\Windows\System32\config\SAM" (
    echo Testing access to C:\Windows\System32\config\SAM...
    type "C:\Windows\System32\config\SAM" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ Can read SAM file
    ) else (
        echo ✗ Cannot read SAM file (access denied or locked)
    )
    
    echo Testing access to C:\Windows\System32\config\SECURITY...
    type "C:\Windows\System32\config\SECURITY" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ Can read SECURITY file
    ) else (
        echo ✗ Cannot read SECURITY file (access denied or locked)
    )
    echo.
)

echo [%time%] Checking registry access...
echo.

echo Testing registry access...
reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v ProductName >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Can access registry
) else (
    echo ✗ Cannot access registry
)
echo.

echo [%time%] Checking privileges...
echo.

net session >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Running as administrator
) else (
    echo ✗ Not running as administrator
    echo   This may cause hive loading to fail
)
echo.

echo [%time%] Testing RegLoadKey functionality...
echo.

echo Testing if we can load a test hive...
reg save "HKLM\SOFTWARE" "%TEMP%\test_hive.hiv" /y >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Can save registry hive
    echo   Testing hive loading...
    reg load "HKLM\TEST_HIVE" "%TEMP%\test_hive.hiv" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ Can load registry hive
        reg unload "HKLM\TEST_HIVE" >nul 2>&1
        echo ✓ Can unload registry hive
    ) else (
        echo ✗ Cannot load registry hive
        echo   Error code: %errorlevel%
    )
    del "%TEMP%\test_hive.hiv" >nul 2>&1
) else (
    echo ✗ Cannot save registry hive
    echo   Error code: %errorlevel%
)
echo.

echo [%time%] Recommendations...
echo.

echo If hive loading fails in the application:
echo.
echo 1. CHECK THE WINDOWS PATH:
echo    - Ensure you're pointing to the correct Windows installation
echo    - The path should be like: C:\Windows, D:\Windows, etc.
echo    - Not: C:\Windows\System32\config (this is just the config folder)
echo.
echo 2. CHECK FILE ACCESS:
echo    - SAM and SECURITY files must be readable
echo    - Files should not be locked by another process
echo    - Try rebooting if files are locked
echo.
echo 3. CHECK PRIVILEGES:
echo    - Must run as administrator
echo    - Need backup/restore privileges (usually automatic in WinPE)
echo.
echo 4. COMMON ERROR CODES:
echo    - Error 5: Access denied - need administrator privileges
echo    - Error 32: File in use - try rebooting
echo    - Error 1314: Privilege not held - need backup/restore privileges
echo    - Error 1392: File corrupted - may need to repair Windows
echo.
echo 5. TESTING STEPS:
echo    - Run this diagnostic script first
echo    - Check the application log window for detailed error messages
echo    - Try with a different Windows installation if available
echo.

echo [%time%] Diagnostic complete
echo.
echo Press any key to exit...
pause >nul 