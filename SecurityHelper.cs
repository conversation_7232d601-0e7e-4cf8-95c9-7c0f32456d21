using System.Security.Cryptography;
using System.Text;

namespace createBBYTESTAccount_Master
{
    public static class SecurityHelper
    {
        /// <summary>
        /// Generates a new SID for a local user account
        /// </summary>
        /// <param name="machineSid">The machine SID (S-1-5-21-xxx-xxx-xxx)</param>
        /// <param name="rid">The relative identifier (RID) for the user</param>
        /// <returns>Complete user SID</returns>
        public static string GenerateUserSid(string machineSid, uint rid)
        {
            return $"{machineSid}-{rid}";
        }

        /// <summary>
        /// Generates the next available RID for a new user
        /// </summary>
        /// <param name="existingRids">Array of existing RIDs</param>
        /// <returns>Next available RID starting from 1001</returns>
        public static uint GenerateNextRid(uint[] existingRids)
        {
            uint nextRid = 1001; // Start from 1001 for regular users
            
            if (existingRids != null && existingRids.Length > 0)
            {
                Array.Sort(existingRids);
                uint maxRid = existingRids[existingRids.Length - 1];
                if (maxRid >= nextRid)
                {
                    nextRid = maxRid + 1;
                }
            }
            
            return nextRid;
        }

        /// <summary>
        /// Creates NT hash for password (used in SAM database)
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <returns>NT hash as byte array</returns>
        public static byte[] CreateNTHash(string password)
        {
            if (string.IsNullOrEmpty(password))
            {
                return new byte[16]; // Empty hash for blank password
            }

            // Convert password to UTF-16 little endian
            byte[] passwordBytes = Encoding.Unicode.GetBytes(password);
            
            // Create MD4 hash
            using (var md4 = MD4.Create())
            {
                return md4.ComputeHash(passwordBytes);
            }
        }

        /// <summary>
        /// Creates LM hash for password (legacy, usually empty for modern systems)
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <returns>LM hash as byte array (usually empty)</returns>
        public static byte[] CreateLMHash(string password)
        {
            // Modern Windows systems don't use LM hashes
            // Return empty hash
            return new byte[16];
        }

        /// <summary>
        /// Converts a RID to its hexadecimal string representation
        /// </summary>
        /// <param name="rid">The RID value</param>
        /// <returns>8-character hex string</returns>
        public static string RidToHex(uint rid)
        {
            return rid.ToString("X8");
        }

        /// <summary>
        /// Converts hex string back to RID
        /// </summary>
        /// <param name="hexRid">8-character hex string</param>
        /// <returns>RID value</returns>
        public static uint HexToRid(string hexRid)
        {
            return Convert.ToUInt32(hexRid, 16);
        }
    }

    /// <summary>
    /// MD4 hash implementation for NT password hashing
    /// </summary>
    public class MD4 : HashAlgorithm
    {
        private uint[] _state = null!;
        private byte[] _buffer = null!;
        private uint _count;

        public MD4()
        {
            Initialize();
        }

        public static new MD4 Create()
        {
            return new MD4();
        }

        public override void Initialize()
        {
            _state = new uint[] { 0x67452301, 0xEFCDAB89, 0x98BADCFE, 0x10325476 };
            _buffer = new byte[64];
            _count = 0;
        }

        protected override void HashCore(byte[] array, int ibStart, int cbSize)
        {
            int i = (int)(_count % 64);
            _count += (uint)cbSize;

            if (i + cbSize >= 64)
            {
                Array.Copy(array, ibStart, _buffer, i, 64 - i);
                ProcessBlock(_buffer, 0);
                
                for (int j = 64 - i; j + 63 < cbSize; j += 64)
                {
                    ProcessBlock(array, ibStart + j);
                }
                
                i = 0;
                cbSize = cbSize % 64;
                ibStart = ibStart + (cbSize - (cbSize % 64));
            }

            Array.Copy(array, ibStart, _buffer, i, cbSize);
        }

        protected override byte[] HashFinal()
        {
            byte[] hash = new byte[16];
            int i = (int)(_count % 64);
            
            _buffer[i++] = 0x80;
            
            if (i > 56)
            {
                Array.Clear(_buffer, i, 64 - i);
                ProcessBlock(_buffer, 0);
                i = 0;
            }
            
            Array.Clear(_buffer, i, 56 - i);
            
            ulong bitCount = _count * 8;
            for (int j = 0; j < 8; j++)
            {
                _buffer[56 + j] = (byte)(bitCount >> (j * 8));
            }
            
            ProcessBlock(_buffer, 0);
            
            for (int j = 0; j < 4; j++)
            {
                for (int k = 0; k < 4; k++)
                {
                    hash[j * 4 + k] = (byte)(_state[j] >> (k * 8));
                }
            }
            
            return hash;
        }

        private void ProcessBlock(byte[] block, int offset)
        {
            uint[] x = new uint[16];
            for (int i = 0; i < 16; i++)
            {
                x[i] = BitConverter.ToUInt32(block, offset + i * 4);
            }

            uint a = _state[0], b = _state[1], c = _state[2], d = _state[3];

            // Round 1
            a = FF(a, b, c, d, x[0], 3); d = FF(d, a, b, c, x[1], 7); c = FF(c, d, a, b, x[2], 11); b = FF(b, c, d, a, x[3], 19);
            a = FF(a, b, c, d, x[4], 3); d = FF(d, a, b, c, x[5], 7); c = FF(c, d, a, b, x[6], 11); b = FF(b, c, d, a, x[7], 19);
            a = FF(a, b, c, d, x[8], 3); d = FF(d, a, b, c, x[9], 7); c = FF(c, d, a, b, x[10], 11); b = FF(b, c, d, a, x[11], 19);
            a = FF(a, b, c, d, x[12], 3); d = FF(d, a, b, c, x[13], 7); c = FF(c, d, a, b, x[14], 11); b = FF(b, c, d, a, x[15], 19);

            // Round 2
            a = GG(a, b, c, d, x[0], 3); d = GG(d, a, b, c, x[4], 5); c = GG(c, d, a, b, x[8], 9); b = GG(b, c, d, a, x[12], 13);
            a = GG(a, b, c, d, x[1], 3); d = GG(d, a, b, c, x[5], 5); c = GG(c, d, a, b, x[9], 9); b = GG(b, c, d, a, x[13], 13);
            a = GG(a, b, c, d, x[2], 3); d = GG(d, a, b, c, x[6], 5); c = GG(c, d, a, b, x[10], 9); b = GG(b, c, d, a, x[14], 13);
            a = GG(a, b, c, d, x[3], 3); d = GG(d, a, b, c, x[7], 5); c = GG(c, d, a, b, x[11], 9); b = GG(b, c, d, a, x[15], 13);

            // Round 3
            a = HH(a, b, c, d, x[0], 3); d = HH(d, a, b, c, x[8], 9); c = HH(c, d, a, b, x[4], 11); b = HH(b, c, d, a, x[12], 15);
            a = HH(a, b, c, d, x[2], 3); d = HH(d, a, b, c, x[10], 9); c = HH(c, d, a, b, x[6], 11); b = HH(b, c, d, a, x[14], 15);
            a = HH(a, b, c, d, x[1], 3); d = HH(d, a, b, c, x[9], 9); c = HH(c, d, a, b, x[5], 11); b = HH(b, c, d, a, x[13], 15);
            a = HH(a, b, c, d, x[3], 3); d = HH(d, a, b, c, x[11], 9); c = HH(c, d, a, b, x[7], 11); b = HH(b, c, d, a, x[15], 15);

            _state[0] += a; _state[1] += b; _state[2] += c; _state[3] += d;
        }

        private static uint FF(uint a, uint b, uint c, uint d, uint x, int s)
        {
            return RotateLeft(a + ((b & c) | (~b & d)) + x, s);
        }

        private static uint GG(uint a, uint b, uint c, uint d, uint x, int s)
        {
            return RotateLeft(a + ((b & c) | (b & d) | (c & d)) + x + 0x5A827999, s);
        }

        private static uint HH(uint a, uint b, uint c, uint d, uint x, int s)
        {
            return RotateLeft(a + (b ^ c ^ d) + x + 0x6ED9EBA1, s);
        }

        private static uint RotateLeft(uint value, int shift)
        {
            return (value << shift) | (value >> (32 - shift));
        }
    }
}
