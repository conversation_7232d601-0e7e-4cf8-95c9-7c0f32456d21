using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace createBBYTESTAccount_Master
{
    public class UserAccountCreator
    {
        private readonly string windowsPath;
        private RegistryHelper? registryHelper;

        public UserAccountCreator(string windowsPath)
        {
            this.windowsPath = windowsPath;
        }

        public bool ValidateWindowsPath()
        {
            try
            {
                string samPath = Path.Combine(windowsPath, "System32", "config", "SAM");
                string securityPath = Path.Combine(windowsPath, "System32", "config", "SECURITY");
                
                return File.Exists(samPath) && File.Exists(securityPath);
            }
            catch
            {
                return false;
            }
        }

        public string GetWindowsVersionInfo()
        {
            try
            {
                string versionPath = Path.Combine(windowsPath, "System32", "config", "SOFTWARE");
                if (File.Exists(versionPath))
                {
                    return "Windows installation found";
                }
                return "Windows installation detected";
            }
            catch
            {
                return "Unknown Windows version";
            }
        }

        public static bool HasRequiredPrivileges()
        {
            try
            {
                using (var identity = System.Security.Principal.WindowsIdentity.GetCurrent())
                {
                    var principal = new System.Security.Principal.WindowsPrincipal(identity);
                    return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
                }
            }
            catch
            {
                return false;
            }
        }

        public bool CreateUserAccount(string username, string password, string fullName, bool isAdmin, Action<string>? statusCallback = null)
        {
            try
            {
                statusCallback?.Invoke("Starting user account creation process...");
                System.Diagnostics.Debug.WriteLine($"Creating user account: {username}");

                // Validate inputs
                if (string.IsNullOrWhiteSpace(username))
                {
                    statusCallback?.Invoke("ERROR: Username cannot be empty");
                    return false;
                }

                if (username.Length > 20)
                {
                    statusCallback?.Invoke("ERROR: Username too long (>20 characters)");
                    return false;
                }

                if (string.IsNullOrEmpty(fullName))
                {
                    fullName = username;
                    statusCallback?.Invoke($"Using username as full name: {fullName}");
                }

                statusCallback?.Invoke("Loading registry hives...");
                
                // Load the registry hives
                if (registryHelper == null)
                {
                    registryHelper = new RegistryHelper(windowsPath);
                }

                if (!registryHelper.LoadSAMHive())
                {
                    statusCallback?.Invoke("ERROR: Failed to load SAM hive");
                    return false;
                }

                if (!registryHelper.LoadSecurityHive())
                {
                    statusCallback?.Invoke("ERROR: Failed to load SECURITY hive");
                    return false;
                }

                statusCallback?.Invoke("Registry hives loaded successfully");

                // Generate a new RID
                statusCallback?.Invoke("Generating new RID for user account...");
                int newRid = GetNextAvailableRid();
                statusCallback?.Invoke($"Generated RID: {newRid}");

                // Create the user account in SAM
                statusCallback?.Invoke("Creating user account in SAM database...");
                if (!CreateSAMUserAccount(username, password, fullName, newRid, statusCallback))
                {
                    statusCallback?.Invoke("ERROR: Failed to create SAM user account");
                    return false;
                }

                // Create corresponding SECURITY entries
                statusCallback?.Invoke("Creating SECURITY hive entries...");
                if (!CreateSecurityUserAccount(username, newRid, statusCallback))
                {
                    statusCallback?.Invoke("ERROR: Failed to create SECURITY user account");
                    return false;
                }

                // Add to groups
                statusCallback?.Invoke("Adding user to groups...");
                if (isAdmin)
                {
                    statusCallback?.Invoke("Adding user to Administrators group...");
                    AddUserToGroup(username, "Administrators", newRid);
                }

                statusCallback?.Invoke("Adding user to Users group...");
                AddUserToGroup(username, "Users", newRid);

                // Create user profile directory
                statusCallback?.Invoke("Creating user profile directory...");
                CreateUserProfile(username);

                // Save and unload the hives
                statusCallback?.Invoke("Saving registry changes...");
                if (registryHelper == null || !registryHelper.SaveAndUnloadHives())
                {
                    statusCallback?.Invoke("ERROR: Failed to save registry changes");
                    return false;
                }

                statusCallback?.Invoke("User account created successfully!");
                return true;
            }
            catch (Exception ex)
            {
                statusCallback?.Invoke($"CRITICAL ERROR: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error creating user account: {ex}");
                return false;
            }
        }

        private bool CreateSAMUserAccount(string username, string password, string fullName, int rid, Action<string>? statusCallback = null)
        {
            try
            {
                statusCallback?.Invoke($"Creating SAM user account for {username} with RID {rid}");

                var samKey = registryHelper?.GetLoadedHive("SAM");
                if (samKey == null)
                {
                    statusCallback?.Invoke("ERROR: SAM hive not loaded");
                    return false;
                }

                // Create the user's key
                string userKeyPath = $"SAM\\Domains\\Account\\Users\\{rid:X8}";
                statusCallback?.Invoke($"Creating user key: {userKeyPath}");

                var userKey = samKey.CreateSubKey(userKeyPath);
                if (userKey == null)
                {
                    statusCallback?.Invoke("ERROR: Failed to create user registry key");
                    return false;
                }

                // Create V value (user data)
                statusCallback?.Invoke("Creating V value (user data)...");
                byte[] vValue = CreateVValue(username, fullName, password, rid);
                userKey.SetValue("V", vValue, RegistryValueKind.Binary);
                statusCallback?.Invoke($"V value created: {vValue.Length} bytes");

                // Create F value (account flags)
                statusCallback?.Invoke("Creating F value (account flags)...");
                byte[] fValue = CreateFValue(username, password, rid);
                userKey.SetValue("F", fValue, RegistryValueKind.Binary);
                statusCallback?.Invoke($"F value created: {fValue.Length} bytes");

                // Create user name index
                statusCallback?.Invoke("Creating username index...");
                string nameIndexPath = $"SAM\\Domains\\Account\\Users\\Names\\{username}";
                var nameIndexKey = samKey.CreateSubKey(nameIndexPath);
                if (nameIndexKey != null)
                {
                    nameIndexKey.SetValue("", rid, RegistryValueKind.DWord);
                    statusCallback?.Invoke("Username index created");
                }

                // Update user count
                statusCallback?.Invoke("Updating user count...");
                UpdateUserCount(samKey, statusCallback);

                statusCallback?.Invoke("SAM user account created successfully");
                return true;
            }
            catch (Exception ex)
            {
                statusCallback?.Invoke($"ERROR creating SAM user account: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error creating SAM user account: {ex}");
                return false;
            }
        }

        private bool CreateSecurityUserAccount(string username, int rid, Action<string>? statusCallback = null)
        {
            try
            {
                statusCallback?.Invoke($"Creating SECURITY user account for {username} with RID {rid}");

                var securityKey = registryHelper?.GetLoadedHive("SECURITY");
                if (securityKey == null)
                {
                    statusCallback?.Invoke("ERROR: SECURITY hive not loaded");
                    return false;
                }

                // Create the user's key in SECURITY
                string userKeyPath = $"SECURITY\\SAM\\Domains\\Account\\Users\\{rid:X8}";
                statusCallback?.Invoke($"Creating SECURITY user key: {userKeyPath}");

                var userKey = securityKey.CreateSubKey(userKeyPath);
                if (userKey == null)
                {
                    statusCallback?.Invoke("ERROR: Failed to create SECURITY user registry key");
                    return false;
                }

                // Create V value (user data)
                statusCallback?.Invoke("Creating SECURITY V value...");
                byte[] vValue = CreateSecurityVValue(username, rid);
                userKey.SetValue("V", vValue, RegistryValueKind.Binary);
                statusCallback?.Invoke($"SECURITY V value created: {vValue.Length} bytes");

                // Create F value (account flags)
                statusCallback?.Invoke("Creating SECURITY F value...");
                byte[] fValue = CreateSecurityFValue(username, rid);
                userKey.SetValue("F", fValue, RegistryValueKind.Binary);
                statusCallback?.Invoke($"SECURITY F value created: {fValue.Length} bytes");

                statusCallback?.Invoke("SECURITY user account created successfully");
                return true;
            }
            catch (Exception ex)
            {
                statusCallback?.Invoke($"ERROR creating SECURITY user account: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error creating SECURITY user account: {ex}");
                return false;
            }
        }

        private int GetNextAvailableRid()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Getting next available RID...");
                
                var samKey = registryHelper?.GetLoadedHive("SAM");
                if (samKey == null)
                {
                    throw new Exception("SAM hive not loaded");
                }

                // Start from RID 1000 (first user account)
                int nextRid = 1000;
                var usersKey = samKey.OpenSubKey("SAM\\Domains\\Account\\Users");
                
                if (usersKey != null)
                {
                    string[] existingRids = usersKey.GetSubKeyNames();
                    System.Diagnostics.Debug.WriteLine($"Found {existingRids.Length} existing user keys");

                    // Find the highest RID
                    foreach (string ridString in existingRids)
                    {
                        if (ridString.Length == 8 && int.TryParse(ridString, System.Globalization.NumberStyles.HexNumber, null, out int rid))
                        {
                            if (rid >= nextRid)
                            {
                                nextRid = rid + 1;
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Next available RID: {nextRid}");
                return nextRid;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting next RID: {ex.Message}");
                throw;
            }
        }

        private void UpdateUserCount(RegistryKey samKey, Action<string>? statusCallback = null)
        {
            try
            {
                statusCallback?.Invoke("Updating user count in SAM...");
                
                var accountKey = samKey.OpenSubKey("SAM\\Domains\\Account", true);
                if (accountKey == null)
                {
                    statusCallback?.Invoke("ERROR: Cannot open Account key for user count update");
                    return;
                }

                // Get current user count
                object? currentCount = accountKey.GetValue("F");
                if (currentCount is byte[] fValue && fValue.Length >= 12)
                {
                    int count = BitConverter.ToInt32(fValue, 8);
                    count++;
                    
                    // Update the count in the F value
                    byte[] newCountBytes = BitConverter.GetBytes(count);
                    Array.Copy(newCountBytes, 0, fValue, 8, 4);
                    accountKey.SetValue("F", fValue, RegistryValueKind.Binary);
                    
                    statusCallback?.Invoke($"Updated user count to: {count}");
                    }
                    else
                {
                    statusCallback?.Invoke("WARNING: Could not read current user count");
                }
            }
            catch (Exception ex)
            {
                statusCallback?.Invoke($"ERROR updating user count: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error updating user count: {ex}");
            }
        }

        private void AddUserToGroup(string username, string groupName, int userRid)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Adding user {username} to group {groupName}");
                
                var samKey = registryHelper?.GetLoadedHive("SAM");
                if (samKey == null)
                {
                    System.Diagnostics.Debug.WriteLine("ERROR: SAM hive not loaded for group membership");
                    return;
                }

                // Get the group RID
                int groupRid = GetGroupRid(groupName);
                if (groupRid == -1)
                {
                    System.Diagnostics.Debug.WriteLine($"ERROR: Could not find group RID for {groupName}");
                    return;
                }

                // Add user to group
                string groupKeyPath = $"SAM\\Domains\\Builtin\\Aliases\\{groupRid:X8}";
                var groupKey = samKey.OpenSubKey(groupKeyPath, true);
                if (groupKey != null)
                {
                    // Get current members
                    object? membersValue = groupKey.GetValue("C");
                    if (membersValue is byte[] members)
                    {
                        // Add the new user RID to the members list
                        var newMembers = new List<byte>(members);
                        newMembers.AddRange(BitConverter.GetBytes(userRid));
                        groupKey.SetValue("C", newMembers.ToArray(), RegistryValueKind.Binary);
                        System.Diagnostics.Debug.WriteLine($"Added user {username} (RID {userRid}) to group {groupName} (RID {groupRid})");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding user to group: {ex.Message}");
            }
        }

        private int GetGroupRid(string groupName)
        {
            // Common group RIDs
            return groupName.ToLower() switch
            {
                "administrators" => 544,
                "users" => 545,
                "guests" => 546,
                "power users" => 547,
                "account operators" => 548,
                "server operators" => 549,
                "print operators" => 550,
                "backup operators" => 551,
                "replicator" => 552,
                _ => -1
            };
        }

        private void CreateUserProfile(string username)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Creating user profile for {username}");

                string profilePath = Path.Combine(windowsPath, "..", "Users", username);
                if (!Directory.Exists(profilePath))
                {
                    Directory.CreateDirectory(profilePath);
                    System.Diagnostics.Debug.WriteLine($"Created user profile directory: {profilePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating user profile: {ex.Message}");
            }
        }

        private byte[] CreateVValue(string username, string fullName, string password, int rid)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Creating V value for user {username}");
                
                // Create a basic V value structure
                var vValue = new List<byte>();
                
                // Header (12 bytes)
                vValue.AddRange(BitConverter.GetBytes(0x0000000C)); // Revision
                vValue.AddRange(BitConverter.GetBytes(0x00000000)); // Reserved
                vValue.AddRange(BitConverter.GetBytes(0x00000000)); // Reserved
                
                // Username (20 bytes, null-padded)
                byte[] usernameBytes = System.Text.Encoding.Unicode.GetBytes(username);
                vValue.AddRange(usernameBytes);
                for (int i = usernameBytes.Length; i < 20; i++)
                {
                    vValue.Add(0);
                }
                
                // Full name (256 bytes, null-padded)
                byte[] fullNameBytes = System.Text.Encoding.Unicode.GetBytes(fullName);
                vValue.AddRange(fullNameBytes);
                for (int i = fullNameBytes.Length; i < 256; i++)
                {
                    vValue.Add(0);
                }
                
                // Comment (256 bytes, null-padded)
                for (int i = 0; i < 256; i++)
                {
                    vValue.Add(0);
                }
                
                // User comment (256 bytes, null-padded)
                for (int i = 0; i < 256; i++)
                {
                    vValue.Add(0);
                }
                
                // Home directory (256 bytes, null-padded)
                for (int i = 0; i < 256; i++)
                {
                    vValue.Add(0);
                }
                
                // Home directory drive (256 bytes, null-padded)
                for (int i = 0; i < 256; i++)
                {
                    vValue.Add(0);
                }
                
                // Script path (256 bytes, null-padded)
                for (int i = 0; i < 256; i++)
                {
                    vValue.Add(0);
                }
                
                // Profile path (256 bytes, null-padded)
                for (int i = 0; i < 256; i++)
                {
                    vValue.Add(0);
                }
                
                // Work stations (256 bytes, null-padded)
                for (int i = 0; i < 256; i++)
                {
                    vValue.Add(0);
                }
                
                // Hours allowed (21 bytes)
                for (int i = 0; i < 21; i++)
                {
                    vValue.Add(0xFF); // All hours allowed
                }
                
                // Bad password count (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Logon count (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Country code (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Code page (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Password hint offset (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Password hint length (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // User ID (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(rid));
                
                // Primary group ID (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(545)); // Users group
                
                // Admin count (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Account flags (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0x00000210)); // Normal account
                
                // Last logon (8 bytes)
                vValue.AddRange(BitConverter.GetBytes(0L));
                
                // Last logoff (8 bytes)
                vValue.AddRange(BitConverter.GetBytes(0L));
                
                // Last password change (8 bytes)
                vValue.AddRange(BitConverter.GetBytes(0L));
                
                // Account expires (8 bytes)
                vValue.AddRange(BitConverter.GetBytes(0L));
                
                // Maximum password age (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Minimum password age (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Force logoff (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Lockout threshold (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Lockout duration (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                // Lockout window (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(0));
                
                System.Diagnostics.Debug.WriteLine($"V value created: {vValue.Count} bytes");
                return vValue.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating V value: {ex.Message}");
                throw;
            }
        }

        private byte[] CreateFValue(string username, string password, int rid)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Creating F value for user {username}");
                
                var fValue = new List<byte>();
                
                // Header (12 bytes)
                fValue.AddRange(BitConverter.GetBytes(0x0000000C)); // Revision
                fValue.AddRange(BitConverter.GetBytes(0x00000000)); // Reserved
                fValue.AddRange(BitConverter.GetBytes(0x00000000)); // Reserved
                
                // User RID (4 bytes)
                fValue.AddRange(BitConverter.GetBytes(rid));
                
                // Account flags (4 bytes)
                fValue.AddRange(BitConverter.GetBytes(0x00000210)); // Normal account
                
                // Last failed logon (8 bytes)
                fValue.AddRange(BitConverter.GetBytes(0L));
                
                // Last successful logon (8 bytes)
                fValue.AddRange(BitConverter.GetBytes(0L));
                
                // Failed logon count (4 bytes)
                fValue.AddRange(BitConverter.GetBytes(0));
                
                // Reserved (4 bytes)
                fValue.AddRange(BitConverter.GetBytes(0));
                
                // Password hash (16 bytes) - empty for now
                for (int i = 0; i < 16; i++)
                {
                    fValue.Add(0);
                }
                
                // Password history (16 bytes) - empty
                for (int i = 0; i < 16; i++)
                {
                    fValue.Add(0);
                }
                
                // LM password hash (16 bytes) - empty
                for (int i = 0; i < 16; i++)
                {
                    fValue.Add(0);
                }
                
                System.Diagnostics.Debug.WriteLine($"F value created: {fValue.Count} bytes");
                return fValue.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating F value: {ex.Message}");
                throw;
            }
        }

        private byte[] CreateSecurityVValue(string username, int rid)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Creating SECURITY V value for user {username}");
                
                // Create a basic SECURITY V value structure (similar to SAM but with security descriptors)
                var vValue = new List<byte>();
                
                // Header (12 bytes)
                vValue.AddRange(BitConverter.GetBytes(0x0000000C)); // Revision
                vValue.AddRange(BitConverter.GetBytes(0x00000000)); // Reserved
                vValue.AddRange(BitConverter.GetBytes(0x00000000)); // Reserved
                
                // User RID (4 bytes)
                vValue.AddRange(BitConverter.GetBytes(rid));
                
                // Security descriptor (variable length)
                // For now, create a minimal security descriptor
                byte[] securityDescriptor = CreateBasicSecurityDescriptor();
                vValue.AddRange(BitConverter.GetBytes(securityDescriptor.Length));
                vValue.AddRange(securityDescriptor);
                
                System.Diagnostics.Debug.WriteLine($"SECURITY V value created: {vValue.Count} bytes");
                return vValue.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating SECURITY V value: {ex.Message}");
                throw;
            }
        }

        private byte[] CreateSecurityFValue(string username, int rid)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Creating SECURITY F value for user {username}");
                
                var fValue = new List<byte>();
                
                // Header (12 bytes)
                fValue.AddRange(BitConverter.GetBytes(0x0000000C)); // Revision
                fValue.AddRange(BitConverter.GetBytes(0x00000000)); // Reserved
                fValue.AddRange(BitConverter.GetBytes(0x00000000)); // Reserved
                
                // User RID (4 bytes)
                fValue.AddRange(BitConverter.GetBytes(rid));
                
                // Account flags (4 bytes)
                fValue.AddRange(BitConverter.GetBytes(0x00000210)); // Normal account
                
                // Security flags (4 bytes)
                fValue.AddRange(BitConverter.GetBytes(0x00000000));
                
                System.Diagnostics.Debug.WriteLine($"SECURITY F value created: {fValue.Count} bytes");
                return fValue.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating SECURITY F value: {ex.Message}");
                throw;
            }
        }

        private byte[] CreateBasicSecurityDescriptor()
        {
            // Create a minimal security descriptor
            var sd = new List<byte>();
            
            // Security descriptor header
            sd.Add(0x01); // Revision
            sd.Add(0x00); // Sbz1
            sd.AddRange(BitConverter.GetBytes(unchecked((short)0x8004))); // Control (Self-relative, DACL present)
            sd.AddRange(BitConverter.GetBytes(0)); // Owner SID offset
            sd.AddRange(BitConverter.GetBytes(0)); // Group SID offset
            sd.AddRange(BitConverter.GetBytes(0)); // SACL offset
            sd.AddRange(BitConverter.GetBytes(0)); // DACL offset
            
            return sd.ToArray();
        }
    }
}
