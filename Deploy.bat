@echo off
echo ========================================
echo Create BBY TEST Account Master - Deployment
echo ========================================
echo.

echo Building application...
dotnet build createBBYTESTAccount_Master.sln --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.
echo Files created in bin\Release\net9.0-windows\:
dir bin\Release\net9.0-windows\*.exe /b
dir bin\Release\net9.0-windows\*.dll /b

echo.
echo ========================================
echo Deployment Instructions:
echo ========================================
echo 1. Copy createBBYTESTAccount_Master.exe to your WinPE environment
echo 2. Copy all files from bin\Release\net9.0-windows\ to the same location
echo 3. Ensure .NET 9.0 runtime is available in WinPE
echo 4. Run with administrator privileges
echo.
echo For WinPE deployment, you may need to:
echo - Add .NET 9.0 runtime support to your WinPE image
echo - Ensure the application runs with elevated privileges
echo.
pause
