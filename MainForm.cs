using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace createBBYTESTAccount_Master
{
    public partial class MainForm : Form
    {
        private UserAccountCreator? _accountCreator;

        public MainForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Check if running in WinPE environment
            bool isWinPE = IsRunningInWinPE();

            if (!isWinPE)
            {
                // Only show privilege warnings if NOT in WinPE
                if (!UserAccountCreator.HasRequiredPrivileges())
                {
                    LogMessage("WARNING: Not running as administrator - this may cause issues");
                    MessageBox.Show(
                        "This application requires administrator privileges to modify registry hives.\n\n" +
                        "Please run as administrator or from WinPE environment.\n\n" +
                        "The application will continue but may not function properly without elevated privileges.",
                        "Insufficient Privileges",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                }
                else
                {
                    LogMessage("✓ Running with administrator privileges");
                }
            }
            else
            {
                // Running in WinPE - show helpful info
                LogMessage("✓ WinPE environment detected");
                MessageBox.Show(
                    "WinPE Environment Detected\n\n" +
                    "You can now create user accounts in offline Windows installations.\n" +
                    "Select the Windows folder of the offline installation (e.g., C:\\Windows).",
                    "WinPE Mode",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }

            // Try to detect common Windows installation paths
            try
            {
                string[] commonPaths = {
                    @"C:\Windows",
                    @"D:\Windows",
                    @"E:\Windows"
                };

                foreach (string path in commonPaths)
                {
                    if (Directory.Exists(path))
                    {
                        txtWindowsPath.Text = path;
                        LogMessage($"Auto-detected Windows path: {path}");
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                // If we can't detect paths, just leave the field empty
                LogMessage($"Error detecting Windows paths: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error detecting Windows paths: {ex.Message}");
            }

            UpdateUI();
        }

        private void LogMessage(string message)
        {
            if (txtLog.InvokeRequired)
            {
                txtLog.Invoke(new Action<string>(LogMessage), message);
            }
            else
            {
                string timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
                txtLog.AppendText($"[{timestamp}] {message}{Environment.NewLine}");
                txtLog.ScrollToCaret();
                Application.DoEvents();
            }
        }

        private void btnBrowseWindows_Click(object sender, EventArgs e)
        {
            try
            {
                LogMessage("Opening folder browser...");
                
                // Try the modern folder browser first (Windows 10+)
                if (TryUseModernFolderBrowser())
                    return;

                // Fallback to classic folder browser
                using (var folderDialog = new FolderBrowserDialog())
                {
                    folderDialog.Description = "Select the Windows installation directory (e.g., C:\\Windows)";
                    folderDialog.ShowNewFolderButton = false;
                    folderDialog.RootFolder = Environment.SpecialFolder.MyComputer;

                    // Set initial directory
                    string initialPath = @"C:\";
                    if (!string.IsNullOrEmpty(txtWindowsPath.Text) && Directory.Exists(txtWindowsPath.Text))
                    {
                        initialPath = txtWindowsPath.Text;
                    }
                    folderDialog.SelectedPath = initialPath;

                    DialogResult result = folderDialog.ShowDialog(this);
                    if (result == DialogResult.OK && !string.IsNullOrEmpty(folderDialog.SelectedPath))
                    {
                        txtWindowsPath.Text = folderDialog.SelectedPath;
                        LogMessage($"Selected Windows path: {folderDialog.SelectedPath}");
                        UpdateUI();
                    }
                    else
                    {
                        LogMessage("Folder selection cancelled");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Folder browser error: {ex.Message}");
                MessageBox.Show(
                    $"Folder browser error: {ex.Message}\n\n" +
                    "Please manually enter the Windows path:\n" +
                    "• C:\\Windows (if Windows is on C: drive)\n" +
                    "• D:\\Windows (if Windows is on D: drive)\n" +
                    "• [Drive]:\\Windows (for other drives)",
                    "Folder Browser Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // Focus on the text box for manual entry
                txtWindowsPath.Focus();
            }
        }

        private bool TryUseModernFolderBrowser()
        {
            try
            {
                // This is a simplified approach - in a full implementation you might use
                // Windows.Storage.Pickers or other modern APIs
                return false; // For now, always use classic browser
            }
            catch
            {
                return false;
            }
        }

        private bool IsRunningInWinPE()
        {
            try
            {
                // Check for WinPE indicators
                // WinPE typically has X: as system drive and specific registry keys
                string systemDrive = Environment.GetEnvironmentVariable("SystemDrive") ?? "";
                bool isXDrive = systemDrive.Equals("X:", StringComparison.OrdinalIgnoreCase);

                // Check for WinPE-specific paths
                bool hasWinPEPaths = Directory.Exists(@"X:\Windows") ||
                                   Environment.GetEnvironmentVariable("PROCESSOR_ARCHITECTURE") != null &&
                                   File.Exists(@"X:\Windows\System32\winpeshl.exe");

                return isXDrive || hasWinPEPaths;
            }
            catch
            {
                return false;
            }
        }

        private void txtWindowsPath_TextChanged(object sender, EventArgs e)
        {
            UpdateUI();
        }

        private void UpdateUI()
        {
            try
            {
                bool hasValidPath = !string.IsNullOrWhiteSpace(txtWindowsPath.Text);
                bool hasValidUsername = !string.IsNullOrWhiteSpace(txtUsername.Text);

                if (hasValidPath)
                {
                    LogMessage($"Validating Windows path: {txtWindowsPath.Text}");
                    
                    _accountCreator = new UserAccountCreator(txtWindowsPath.Text);
                    bool isValidWindows = _accountCreator.ValidateWindowsPath();

                    if (isValidWindows)
                    {
                        string versionInfo = _accountCreator.GetWindowsVersionInfo();
                        lblWindowsVersion.Text = versionInfo;
                        lblWindowsVersion.ForeColor = System.Drawing.Color.Green;
                        LogMessage($"✓ Windows installation validated: {versionInfo}");
                    }
                    else
                    {
                        lblWindowsVersion.Text = "Invalid Windows installation";
                        lblWindowsVersion.ForeColor = System.Drawing.Color.Red;
                        LogMessage("✗ Invalid Windows installation - SAM/SECURITY files not found");
                    }

                    btnCreateAccount.Enabled = isValidWindows && hasValidUsername;
                }
                else
                {
                    lblWindowsVersion.Text = "No Windows path selected";
                    lblWindowsVersion.ForeColor = System.Drawing.Color.Gray;
                    btnCreateAccount.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                lblWindowsVersion.Text = $"Error: {ex.Message}";
                lblWindowsVersion.ForeColor = System.Drawing.Color.Red;
                btnCreateAccount.Enabled = false;
                LogMessage($"Error in UpdateUI: {ex.Message}");

                System.Diagnostics.Debug.WriteLine($"UpdateUI Error: {ex}");
            }
        }

        private void txtUsername_TextChanged(object sender, EventArgs e)
        {
            UpdateUI();
        }

        private async void btnCreateAccount_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate inputs
                string username = txtUsername.Text.Trim();
                string password = txtPassword.Text;
                string fullName = txtFullName.Text.Trim();
                bool isAdmin = chkIsAdmin.Checked;

                LogMessage("=== Starting Account Creation Process ===");
                LogMessage($"Username: {username}");
                LogMessage($"Full Name: {fullName}");
                LogMessage($"Administrator: {isAdmin}");
                LogMessage($"Windows Path: {txtWindowsPath.Text}");

                if (string.IsNullOrWhiteSpace(username))
                {
                    LogMessage("ERROR: Username is empty");
                    MessageBox.Show("Please enter a username.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (username.Length > 20)
                {
                    LogMessage("ERROR: Username too long (>20 characters)");
                    MessageBox.Show("Username cannot be longer than 20 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                // Check for invalid characters
                char[] invalidChars = { '\\', '/', ':', '*', '?', '"', '<', '>', '|' };
                if (username.IndexOfAny(invalidChars) >= 0)
                {
                    LogMessage("ERROR: Username contains invalid characters");
                    MessageBox.Show("Username contains invalid characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrEmpty(fullName))
                {
                    fullName = username;
                    LogMessage($"Using username as full name: {fullName}");
                }

                // Confirm the action
                string confirmMessage = $"Create user account with the following details?\n\n" +
                    $"Username: {username}\n" +
                    $"Full Name: {fullName}\n" +
                    $"Administrator: {(isAdmin ? "Yes" : "No")}\n" +
                    $"Windows Path: {txtWindowsPath.Text}";

                if (MessageBox.Show(confirmMessage, "Confirm Account Creation",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                {
                    LogMessage("Account creation cancelled by user");
                    return;
                }

                // Disable UI during operation
                SetUIEnabled(false);
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                lblStatus.Text = "Creating user account...";

                // Create account asynchronously
                bool success = await Task.Run(() =>
                {
                    return _accountCreator!.CreateUserAccount(username, password, fullName, isAdmin,
                        (status) => {
                            Invoke(new Action(() => {
                                LogMessage(status);
                                lblStatus.Text = status;
                            }));
                        });
                });

                if (success)
                {
                    LogMessage("=== Account Creation SUCCESSFUL ===");
                    lblStatus.Text = "User account created successfully!";
                    lblStatus.ForeColor = System.Drawing.Color.Green;

                    MessageBox.Show(
                        $"User account '{username}' has been created successfully!\n\n" +
                        "The account will be available when Windows boots normally.",
                        "Success",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    // Clear form
                    ClearForm();
                }
                else
                {
                    LogMessage("=== Account Creation FAILED ===");
                    lblStatus.Text = "Failed to create user account.";
                    lblStatus.ForeColor = System.Drawing.Color.Red;
                    
                    MessageBox.Show(
                        "Failed to create user account. Check the log for detailed error information.",
                        "Error",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"=== CRITICAL ERROR ===: {ex.Message}");
                LogMessage($"Stack trace: {ex.StackTrace}");
                lblStatus.Text = $"Error: {ex.Message}";
                lblStatus.ForeColor = System.Drawing.Color.Red;

                MessageBox.Show(
                    $"An error occurred while creating the user account:\n\n{ex.Message}\n\nCheck the log for more details.",
                    "Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            finally
            {
                SetUIEnabled(true);
                progressBar.Visible = false;
            }
        }

        private void SetUIEnabled(bool enabled)
        {
            txtWindowsPath.Enabled = enabled;
            btnBrowseWindows.Enabled = enabled;
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            txtFullName.Enabled = enabled;
            chkIsAdmin.Enabled = enabled;
            btnCreateAccount.Enabled = enabled && !string.IsNullOrWhiteSpace(txtUsername.Text);
        }

        private void ClearForm()
        {
            txtUsername.Clear();
            txtPassword.Clear();
            txtFullName.Clear();
            chkIsAdmin.Checked = false;
            lblStatus.Text = "Ready";
            lblStatus.ForeColor = System.Drawing.Color.Black;
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            lblStatus.Text = "Ready";
            lblStatus.ForeColor = System.Drawing.Color.Black;
            LogMessage("Application started");
        }

        private void btnClearLog_Click(object sender, EventArgs e)
        {
            txtLog.Clear();
            LogMessage("Log cleared");
        }

        private void btnSaveLog_Click(object sender, EventArgs e)
        {
            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "Text files (*.txt)|*.txt|All files (*.*)|*.*";
                    saveDialog.FileName = $"UserAccountCreator_Log_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                    
                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        File.WriteAllText(saveDialog.FileName, txtLog.Text);
                        LogMessage($"Log saved to: {saveDialog.FileName}");
                        MessageBox.Show($"Log saved successfully to:\n{saveDialog.FileName}", "Log Saved", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error saving log: {ex.Message}");
                MessageBox.Show($"Error saving log: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

