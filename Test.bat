@echo off
echo ========================================
echo User Account Creator - Debug Test Script
echo ========================================
echo.

echo [%time%] Starting debug test...
echo.

echo [%time%] Checking Windows installation...
if not exist "C:\Windows\System32\config\SAM" (
    echo ERROR: SAM file not found at C:\Windows\System32\config\SAM
    echo Please ensure you're running this on a Windows system or specify the correct Windows path
    goto :error
)

if not exist "C:\Windows\System32\config\SECURITY" (
    echo ERROR: SECURITY file not found at C:\Windows\System32\config\SECURITY
    echo Please ensure you're running this on a Windows system or specify the correct Windows path
    goto :error
)

echo [%time%] ✓ Windows installation found
echo.

echo [%time%] Checking file permissions...
echo SAM file size: 
dir "C:\Windows\System32\config\SAM" | find "SAM"
echo SECURITY file size:
dir "C:\Windows\System32\config\SECURITY" | find "SECURITY"
echo.

echo [%time%] Checking registry access...
reg query "HKLM\SAM" >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Cannot access SAM registry hive directly
    echo This is normal - SAM hive is protected
) else (
    echo ✓ Can access SAM registry hive
)

reg query "HKLM\SECURITY" >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Cannot access SECURITY registry hive directly
    echo This is normal - SECURITY hive is protected
) else (
    echo ✓ Can access SECURITY registry hive
)
echo.

echo [%time%] Checking user privileges...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Not running as administrator
    echo Please run this script as administrator
    goto :error
) else (
    echo ✓ Running as administrator
)
echo.

echo [%time%] Checking for existing test accounts...
reg query "HKLM\SAM\SAM\Domains\Account\Users\Names\BBYTEST" >nul 2>&1
if %errorlevel% equ 0 (
    echo WARNING: BBYTEST account already exists
    echo You may want to delete it first or use a different username
) else (
    echo ✓ No existing BBYTEST account found
)
echo.

echo [%time%] Checking .NET runtime...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET runtime not found
    echo Please install .NET 9.0 or later
    goto :error
) else (
    echo ✓ .NET runtime found
    dotnet --version
)
echo.

echo [%time%] Building application...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    goto :error
) else (
    echo ✓ Build successful
)
echo.

echo [%time%] Running application...
echo Starting User Account Creator application...
echo.
echo IMPORTANT: When the application starts:
echo 1. Set Windows path to: C:\Windows
echo 2. Enter username: BBYTEST
echo 3. Enter password: Test123!
echo 4. Enter full name: BBY Test Account
echo 5. Check "Make this user an administrator"
echo 6. Click "Create Account"
echo 7. Watch the log window for detailed progress
echo.
echo Press any key to start the application...
pause >nul

start "" "bin\Release\net9.0-windows\createBBYTESTAccount_Master.exe"

echo.
echo [%time%] Application started
echo.
echo ========================================
echo DEBUGGING TIPS:
echo ========================================
echo.
echo If account creation fails:
echo.
echo 1. CHECK THE LOG WINDOW:
echo    - Look for specific error messages
echo    - Note the step where it failed
echo    - Check for privilege or access errors
echo.
echo 2. COMMON ISSUES:
echo    - 0xC00002E3: Registry corruption or invalid structure
echo    - Access denied: Insufficient privileges
echo    - File not found: Incorrect Windows path
echo    - Hive load failure: SAM/SECURITY files locked
echo.
echo 3. TROUBLESHOOTING STEPS:
echo    - Ensure running as administrator
echo    - Verify Windows path is correct
echo    - Check that SAM/SECURITY files exist
echo    - Try rebooting if files are locked
echo    - Check Windows Event Log for errors
echo.
echo 4. VERIFICATION:
echo    - After successful creation, check:
echo      reg query "HKLM\SAM\SAM\Domains\Account\Users\Names\BBYTEST"
echo      reg query "HKLM\SECURITY\SAM\Domains\Account\Users\Names\BBYTEST"
echo    - Reboot and try logging in with the new account
echo.
echo Press any key to exit...
pause >nul
goto :end

:error
echo.
echo ========================================
echo TEST FAILED
echo ========================================
echo.
echo Please fix the issues above and try again.
echo.
echo For additional help:
echo 1. Check the application log window
echo 2. Review the README.md file
echo 3. Ensure you have administrator privileges
echo 4. Verify the Windows installation path
echo.
pause
exit /b 1

:end
echo.
echo ========================================
echo TEST COMPLETED
echo ========================================
echo.
echo The application should now be running.
echo Check the log window for detailed information.
echo.
