# Windows User Account Creator

A C# Windows Forms application for creating local user accounts in offline Windows installations by directly modifying the SAM and SECURITY registry hives.

## Overview

This application allows you to create Windows user accounts by manipulating the registry hives directly, which is useful for:
- Offline Windows installations
- WinPE environments
- System recovery scenarios
- Automated deployment

## Features

- **Real Windows Registry Patterns**: Uses actual Windows SAM and SECURITY hive structures
- **Complete Account Creation**: Creates all necessary registry entries for valid user accounts
- **Administrator Support**: Option to create accounts with administrator privileges
- **Input Validation**: Comprehensive validation to prevent registry corruption
- **Error Handling**: Detailed error reporting and troubleshooting
- **Fresh Hive Support**: Works with clean Windows installations

## Requirements

- Windows 10/11 (x64)
- .NET 9.0 Runtime
- Administrator privileges (recommended)
- HIVES folder with exported registry files

## Installation

1. Download the application files
2. Ensure the `HIVES` folder contains the required registry files:
   - `SAM_HIVE_DEFAULT.reg`
   - `SECURITY_HIVE_DEFAULT.reg`
   - `SOFTWARE_HIVE_DEFAULT_PROFILE.reg`
3. Run `createBBYTESTAccount_Master.exe`

## Usage

### Basic Account Creation

1. Launch the application
2. Enter a username (1-20 characters)
3. Enter a password
4. Check "Create as Administrator" if desired
5. Click "Create User Account"

### Testing

1. Click "Test System" to verify hive files and registry access
2. Click "Browse HIVES" to open the hive files folder
3. Check the status message for initialization results

## Registry Structure

The application creates accounts using the following Windows registry structure:

### SAM Hive
```
HKLM\SAM\SAM\Domains\Account\Users\[RID]\
├── F (80 bytes): Account control flags and metadata
├── V (variable): User account data, passwords, group memberships
└── Names\[username]: Username to RID mapping
```

### SECURITY Hive
```
HKLM\SECURITY\Policy\Accounts\[SID]\
├── Sid: Security identifier
└── SecDesc: Security descriptor
```

### SOFTWARE Hive
```
HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\ProfileList\[SID]\
├── ProfileImagePath: User profile path
├── Flags: Profile flags
├── State: Profile state
└── Sid: Security identifier
```

## Account Types

### Regular User
- RID: 1000-9999 (auto-assigned)
- Group: Users (RID 513)
- Permissions: Standard user access

### Administrator
- RID: 1000-9999 (auto-assigned)
- Groups: Users (RID 513), Administrators (RID 544)
- Permissions: Full system access

## Technical Details

### V Value Structure
The V value contains:
- Account flags and control bits
- Password hash (NTLM)
- Group memberships
- Username and full name
- Security descriptor
- Timestamps

### F Value Structure
The F value contains:
- Account control flags
- Last login timestamp
- Password last set timestamp
- Account type and status

### Password Hashing
- Uses MD4 algorithm for NTLM hash generation
- Compatible with Windows authentication
- Supports Unicode passwords

## Troubleshooting

### Common Issues

1. **"Account creator not initialized"**
   - Check that all hive files exist in the HIVES folder
   - Verify file permissions and accessibility

2. **"No available RIDs"**
   - All RIDs in range 1000-9999 are in use
   - Consider using a different RID range

3. **Registry access errors**
   - Run as administrator
   - Check Windows is not hibernated
   - Verify registry permissions

4. **Account not appearing after reboot**
   - Ensure all hive files were saved properly
   - Check for registry corruption
   - Verify group memberships are correct

### Error Codes

- **0xC00002E3**: Registry corruption or invalid structure
  - Check input validation
  - Verify registry structure
  - Ensure proper privileges

### Debug Information

The application provides detailed status messages:
- Initialization status
- Account creation progress
- Error details and suggestions

## Security Considerations

- **Administrator Privileges**: Required for registry modifications
- **Password Security**: Passwords are hashed using Windows-compatible algorithms
- **Registry Integrity**: Input validation prevents corruption
- **Backup**: Always backup registry before modifications

## Development

### Building from Source

1. Install Visual Studio 2022 with .NET 9.0 SDK
2. Open `createBBYTESTAccount_Master.sln`
3. Build the solution
4. Ensure HIVES folder is copied to output directory

### Key Classes

- **UserAccountCreator**: Main account creation logic
- **RegistryHelper**: Registry hive loading/unloading
- **SecurityHelper**: Security descriptor creation
- **MainForm**: User interface

### Dependencies

- Microsoft.Win32 (Registry access)
- System.Security.Cryptography (Password hashing)
- System.Windows.Forms (User interface)

## License

This project is provided as-is for educational and recovery purposes.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test script (`Test.bat`)
3. Review error messages and status information
4. Ensure all requirements are met

## Version History

- **v2.0**: Complete rewrite using real Windows registry patterns
- **v1.0**: Initial implementation with basic functionality

---

**Note**: This application modifies critical Windows registry structures. Always backup your system before use and test in a safe environment first.
